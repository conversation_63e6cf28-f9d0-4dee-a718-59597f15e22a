$passoptions.latex()$
\documentclass[
$for(babel-otherlangs)$
  $babel-otherlangs$,
$endfor$
$if(babel-lang)$
  $babel-lang$,
$endif$
11pt,
$if(papersize)$
  $papersize$paper,
$endif$
$for(classoption)$
  $classoption$$sep$,
$endfor$
]{$documentclass$}
$if(beamerarticle)$
\usepackage{beamerarticle} % needs to be loaded first
$endif$
\usepackage{xcolor}
\usepackage[margin=2.5cm]{geometry}
\usepackage{amsmath,amssymb}
\usepackage{lastpage}

\usepackage{ltablex}       % Combines longtable + tabularx
\keepXColumns              % Retains X columns in longtable mode
\usepackage[table]{xcolor} % For optional banded rows
\usepackage{booktabs}      % Optional: nicer horizontal lines (\toprule etc.)
\usepackage{ragged2e}
\usepackage{array}

\AtBeginEnvironment{longtable}{\scriptsize}
$--
$-- section numbering
$--
$if(numbersections)$
\setcounter{secnumdepth}{$if(secnumdepth)$$secnumdepth$$else$5$endif$}
$else$
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
$endif$
\usepackage{fontspec}
\setmainfont{Calibri}

% Title and section formatting
\usepackage{titlesec}
\titleformat{\section}{\Large\bfseries}{\thesection}{1em}{}
\titlespacing*{\section}{0pt}{12pt plus 4pt minus 2pt}{6pt plus 2pt minus 1pt}
\titleformat{\subsection}{\large\bfseries}{\thesubsection}{1em}{}
\titlespacing*{\subsection}{0pt}{10pt plus 3pt minus 2pt}{4pt plus 2pt minus 1pt}
\titleformat{\subsubsection}{\normalsize\bfseries}{\thesubsubsection}{1em}{}
\titlespacing*{\subsubsection}{0pt}{8pt plus 2pt minus 1pt}{3pt plus 1pt minus 1pt}

% Prevent page breaks after section headings
\usepackage{needspace}
\let\oldsection\section
\renewcommand{\section}[1]{\needspace{8\baselineskip}\oldsection{#1}}
\let\oldsubsection\subsection
\renewcommand{\subsection}[1]{\needspace{6\baselineskip}\oldsubsection{#1}}
\let\oldsubsubsection\subsubsection
\renewcommand{\subsubsection}[1]{\needspace{4\baselineskip}\oldsubsubsection{#1}}

% Additional settings to keep content together
\usepackage{float}
\floatplacement{figure}{H}  % Force figures to stay where they are placed
\raggedbottom  % Allow variable page heights to avoid stretching content

% Make document title bold
\usepackage{etoolbox}
\makeatletter
\patchcmd{\@maketitle}{\@title}{\textbf{\Huge\@title}}{}{}
\makeatother

% Figure caption formatting
\usepackage{caption}
\captionsetup{
    font={small,it},
    skip=5pt,
    aboveskip=5pt,
    belowskip=0pt,
    justification=raggedright,
    singlelinecheck=false
}
$common.latex()$
$for(header-includes)$
$header-includes$
$endfor$
$after-header-includes.latex()$
$hypersetup.latex()$

$if(title)$
\title{$title$$if(thanks)$\thanks{$thanks$}$endif$}
$endif$

$if(subtitle)$
\usepackage{etoolbox}
\makeatletter
\providecommand{\subtitle}[1]{% add subtitle to \maketitle
  \apptocmd{\@title}{\par {\LARGE #1 \par}}{}{}
}
\makeatother
\subtitle{$subtitle$}

$endif$
\author{$for(author)$$author$$sep$ \and $endfor$}
\date{$date$}

\newcommand{\revision}{Rev $revision$}

\usepackage{graphicx}
\usepackage{fancyhdr}
\pagestyle{fancy}
\fancyhead[L]{$route_id$}
\fancyhead[C]{}
\fancyhead[R]{\includegraphics[height=0.8cm]{jedsy_logo_main.png}}  % Ensure logo.png is in the same folder
\fancyfoot[L]{\revision}
\fancyfoot[C]{}
\fancyfoot[R]{Page \thepage\ / \pageref{LastPage}}


\begin{document}
$if(has-frontmatter)$
\frontmatter
$endif$
$if(title)$
\maketitle
\thispagestyle{fancy}
$if(abstract)$
\begin{abstract}
$abstract$
\end{abstract}
$endif$
$endif$

$for(include-before)$
$include-before$

$endfor$
$if(toc)$
$if(toc-title)$
\renewcommand*\contentsname{$toc-title$}
$endif$
{
$if(colorlinks)$
\hypersetup{linkcolor=$if(toccolor)$$toccolor$$else$$endif$}
$endif$
\setcounter{tocdepth}{$toc-depth$}
\tableofcontents
}
$endif$
$if(lof)$
\listoffigures
$endif$
$if(lot)$
\listoftables
$endif$
$if(linestretch)$
\setstretch{$linestretch$}
$endif$
$if(has-frontmatter)$
\mainmatter
$endif$
$body$

$if(has-frontmatter)$
\backmatter
$endif$
$if(nocite-ids)$
\nocite{$for(nocite-ids)$$it$$sep$, $endfor$}
$endif$
$if(natbib)$
$if(bibliography)$
$if(biblio-title)$
$if(has-chapters)$
\renewcommand\bibname{$biblio-title$}
$else$
\renewcommand\refname{$biblio-title$}
$endif$
$endif$
\bibliography{$for(bibliography)$$bibliography$$sep$,$endfor$}

$endif$
$endif$
$if(biblatex)$
\printbibliography$if(biblio-title)$[title=$biblio-title$]$endif$

$endif$
$for(include-after)$
$include-after$

$endfor$
\end{document}
