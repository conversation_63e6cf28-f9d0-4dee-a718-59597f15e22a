import psycopg2
from qgis.utils import iface
from datetime import date

# === 1. DB connection ===
conn = psycopg2.connect(
    dbname="jedsy",
    user="postgres",
    password="StrongSecurePassword123",  # <-- use your password
    host="localhost",
    port="5432"
)
cur = conn.cursor()

# === 2. Insert or update mission record ===
mission_data = {
    "code": "024",
    "title": "024_Ka<PERSON>_Nordhessen",
    "rev": "3",
    "desc": "Test Test Test",
    "date": date.today()
}

# Check if mission already exists
cur.execute("""
    SELECT id FROM missions WHERE mission_code = %s AND revision = %s
""", (mission_data["code"], mission_data["rev"]))
row = cur.fetchone()

if row:
    # Update existing mission
    mission_id = row[0]
    cur.execute("""
        UPDATE missions
        SET title = %s, description = %s, date = %s
        WHERE id = %s
    """, (mission_data["title"], mission_data["desc"], mission_data["date"], mission_id))

    # Clear existing images and targets
    cur.execute("DELETE FROM images WHERE mission_id = %s", (mission_id,))
    cur.execute("DELETE FROM targets WHERE mission_id = %s", (mission_id,))
else:
    # Insert new mission
    cur.execute("""
        INSERT INTO missions (mission_code, title, revision, description, date)
        VALUES (%s, %s, %s, %s, %s)
        RETURNING id
    """, (mission_data["code"], mission_data["title"], mission_data["rev"],
          mission_data["desc"], mission_data["date"]))
    mission_id = cur.fetchone()[0]

conn.commit()


# === 3. Export current map canvas ===
export_path = f"E:/jedsy_exports/{mission_data['code']}_canvas_{mission_data['rev']}.png"

canvas = iface.mapCanvas()
image = canvas.grab()  # Grabs a snapshot of the visible canvas
image.save(export_path, "PNG")

# === 4. Insert image record ===
cur.execute("""
    INSERT INTO images (mission_id, type, path, format, revision)
    VALUES (%s, %s, %s, %s, %s)
""", (mission_id, 'overview', export_path, 'png', mission_data["rev"]))
conn.commit()

print(f"✅ Exported canvas and logged mission {mission_data['code']} Rev {mission_data['rev']}")

from qgis.core import QgsProject, QgsCoordinateTransform, QgsCoordinateReferenceSystem

# === 5. Export from vector layer ===
layer = QgsProject.instance().mapLayersByName("024_Targets")[0]

# Collect current IDs from layer
current_ids = set()
for feat in layer.getFeatures():
    current_ids.add(feat["id"])


# Prepare coordinate transform to EPSG:4326
source_crs = layer.crs()
target_crs = QgsCoordinateReferenceSystem("EPSG:4326")
transform = QgsCoordinateTransform(source_crs, target_crs, QgsProject.instance())

for feat in layer.getFeatures():
    attrs = feat.attributes()

    geom = feat.geometry()
    geom.transform(transform)
    point = geom.asPoint()
    point_wkt = f"POINT({point.x()} {point.y()})"


    # Extract attributes safely by name
    def safe(val):
        return str(val) if val is not None else ""

    name = safe(feat["name"])
    descr = safe(feat["descr"])
    address = safe(feat["address"])
    phone = safe(feat["phone"])
    status = safe(feat["status"])
    contact = safe(feat["contact"])
    email = safe(feat["email"])

    target_id = feat["id"]  # Make sure this is set correctly from the feature attributes

    cur.execute("""
        INSERT INTO targets (id, mission_id, name, descr, address, phone, status, contact, email, coords)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, ST_GeomFromText(%s, 4326))
        ON CONFLICT (mission_id, id)
        DO UPDATE SET
            name = EXCLUDED.name,
            descr = EXCLUDED.descr,
            address = EXCLUDED.address,
            phone = EXCLUDED.phone,
            status = EXCLUDED.status,
            contact = EXCLUDED.contact,
            email = EXCLUDED.email,
            coords = EXCLUDED.coords
    """, (target_id, mission_id, name, descr, address, phone, status, contact, email, point_wkt))

# Delete obsolete targets for this mission
sql = """
    DELETE FROM targets
    WHERE mission_id = %s AND id NOT IN %s
"""
cur.execute(sql, (mission_id, tuple(current_ids)))


conn.commit()
print(f"✅ Exported targets")

cur.close()
conn.close()