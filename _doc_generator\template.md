{# Page 1 #}

**Mission Code:** {{ mission_code }}  
**Revision:** {{ revision }}  
**Date:** {{ date }}

\newpage

## Description

{{ description }}

\newpage

# Maps

## Overview
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{Overview Map}
\end{figure}

## Air risk
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{Air Risk Map}
\end{figure}

## ICAO
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{ICAO Airspace Map}
\end{figure}


\newpage

# Targets

\fontsize{8pt}{10pt}\selectfont
\rowcolors{2}{gray!10}{white}
\renewcommand{\arraystretch}{1.2}

\begin{longtable}{|>{\raggedright\arraybackslash}X|%
                  >{\raggedright\arraybackslash}X|%
                  >{\raggedright\arraybackslash}X|%
                  >{\raggedright\arraybackslash}X|%
                  >{\raggedright\arraybackslash}X|}
\hline
\textbf{ID} & \textbf{Name} & \textbf{Address} & \textbf{Contacts} & \textbf{Coords} \\
\hline
\endfirsthead

\hline
\textbf{ID} & \textbf{Name} & \textbf{Address} & \textbf{Contacts} & \textbf{Coords} \\
\hline
\endhead

\hline
\multicolumn{5}{r}{\textit{Continued on next page...}} \\
\endfoot

\hline
\endlastfoot

{% for row in targets -%}
{{ row.id | escape_tex }} &
{{ row.name | default("–") | escape_tex }} &
{{ row.address | default("–") | escape_tex }} &
{% if row.contact and row.phone and row.contact != "–" and row.phone != "–" -%}
{{ row.contact | escape_tex }} \\ {{ row.phone | escape_tex }}
{%- elif row.contact and row.contact != "–" -%}
{{ row.contact | escape_tex }}
{%- elif row.phone and row.phone != "–" -%}
{{ row.phone | escape_tex }}
{%- else -%}
–
{%- endif %} &
{% if row.lat and row.lon and row.lat != "–" and row.lon != "–" -%}
{{ row.lat | escape_tex }}, {{ row.lon | escape_tex }}
{%- elif row.lat and row.lat != "–" -%}
{{ row.lat | escape_tex }}
{%- elif row.lon and row.lon != "–" -%}
{{ row.lon | escape_tex }}
{%- else -%}
–
{%- endif %} \\
\hline
{%- endfor %}
\end{longtable}

