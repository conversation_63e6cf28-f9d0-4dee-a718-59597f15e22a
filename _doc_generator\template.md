{# Page 1 #}

**Mission Code:** {{ mission_code }}  
**Revision:** {{ revision }}  
**Date:** {{ date }}

\newpage

## Description

{{ description }}

\newpage

# Maps

## Overview
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{Overview Map}
\end{figure}

## Air risk
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{Air Risk Map}
\end{figure}

## ICAO
\begin{figure}[H]
\centering
\includegraphics[width=\textwidth, height=0.85\textheight, keepaspectratio]{ {{ image_path }} }
\caption{ICAO Airspace Map}
\end{figure}


\newpage

# Targets

\fontsize{8pt}{10pt}\selectfont
\rowcolors{2}{gray!10}{white}
\renewcommand{\arraystretch}{1.2}

\begin{longtable}{|>{\raggedright\arraybackslash}p{0.5cm}|%
                  >{\raggedright\arraybackslash}p{4.0cm}|%
                  >{\raggedright\arraybackslash}p{4.0cm}|%
                  >{\raggedright\arraybackslash}p{4.0cm}|%
                  >{\raggedright\arraybackslash}p{2.5cm}|}
\hline
\textbf{ID} & \textbf{Name} & \textbf{Address} & \textbf{Contacts} & \textbf{Coords} \\
\hline
\endfirsthead

\hline
\textbf{ID} & \textbf{Name} & \textbf{Address} & \textbf{Contacts} & \textbf{Coords} \\
\hline
\endhead

\hline
\multicolumn{5}{r}{\textit{Continued on next page...}} \\
\endfoot

\hline
\endlastfoot

{% for row in targets -%}
{%- if row.status == "Active" and (row.descr == "Target" or row.descr == "Jedport") -%}
{{ row.id | escape_tex if row.id and row.id != "NULL" else "" }} &
{{ row.name | escape_tex if row.name and row.name != "NULL" else "" }} &
{{ row.address | escape_tex if row.address and row.address != "NULL" else "" }} &
{% if row.contact and row.contact != "NULL" and row.phone and row.phone != "NULL" -%}
\parbox[c]{3.8cm}{\raggedright {{ row.contact | escape_tex }} \\ {{ row.phone | escape_tex }}}
{%- elif row.contact and row.contact != "NULL" -%}
\parbox[c]{3.8cm}{\raggedright {{ row.contact | escape_tex }}}
{%- elif row.phone and row.phone != "NULL" -%}
\parbox[c]{3.8cm}{\raggedright {{ row.phone | escape_tex }}}
{%- endif %} &
{% if row.lat and row.lat != "NULL" and row.lon and row.lon != "NULL" -%}
{{ row.lat | escape_tex }}, {{ row.lon | escape_tex }}
{%- elif row.lat and row.lat != "NULL" -%}
{{ row.lat | escape_tex }}
{%- elif row.lon and row.lon != "NULL" -%}
{{ row.lon | escape_tex }}
{%- endif %} \\
\hline
{%- endif -%}
{%- endfor %}
\end{longtable}

